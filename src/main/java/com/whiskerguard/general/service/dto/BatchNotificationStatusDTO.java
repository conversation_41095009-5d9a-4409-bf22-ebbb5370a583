package com.whiskerguard.general.service.dto;

import java.time.Instant;

/**
 * 批量通知状态DTO
 * 
 * <AUTHOR> @version 1.0
 * @date 2025-06-23
 */
public class BatchNotificationStatusDTO {

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 状态
     */
    private String status;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 已发送数量
     */
    private Integer sentCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failedCount;

    /**
     * 开始时间
     */
    private Instant startTime;

    /**
     * 完成时间
     */
    private Instant completedTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 进度百分比
     */
    private Double progressPercentage;

    // 默认构造函数
    public BatchNotificationStatusDTO() {}

    // 构造函数
    public BatchNotificationStatusDTO(String batchId, String status) {
        this.batchId = batchId;
        this.status = status;
    }

    // Getters and Setters
    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getSentCount() {
        return sentCount;
    }

    public void setSentCount(Integer sentCount) {
        this.sentCount = sentCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(Integer failedCount) {
        this.failedCount = failedCount;
    }

    public Instant getStartTime() {
        return startTime;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getCompletedTime() {
        return completedTime;
    }

    public void setCompletedTime(Instant completedTime) {
        this.completedTime = completedTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Double getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Double progressPercentage) {
        this.progressPercentage = progressPercentage;
    }

    /**
     * 计算进度百分比
     */
    public void calculateProgress() {
        if (totalCount != null && totalCount > 0) {
            int processed = (sentCount != null ? sentCount : 0);
            this.progressPercentage = (double) processed / totalCount * 100;
        } else {
            this.progressPercentage = 0.0;
        }
    }

    @Override
    public String toString() {
        return "BatchNotificationStatusDTO{" +
            "batchId='" + batchId + '\'' +
            ", status='" + status + '\'' +
            ", totalCount=" + totalCount +
            ", sentCount=" + sentCount +
            ", successCount=" + successCount +
            ", failedCount=" + failedCount +
            ", startTime=" + startTime +
            ", completedTime=" + completedTime +
            ", errorMessage='" + errorMessage + '\'' +
            ", progressPercentage=" + progressPercentage +
            '}';
    }
}
