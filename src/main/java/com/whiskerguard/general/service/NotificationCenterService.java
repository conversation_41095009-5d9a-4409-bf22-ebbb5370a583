package com.whiskerguard.general.service;

import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.service.dto.NotificationRequestDTO;
import com.whiskerguard.general.service.dto.BatchNotificationRequestDTO;
import com.whiskerguard.general.service.dto.SystemNotificationRequestDTO;
import com.whiskerguard.general.service.dto.TaskNotificationRequestDTO;
import com.whiskerguard.general.service.dto.UserNotificationRequestDTO;
import com.whiskerguard.general.service.dto.BatchNotificationStatusDTO;
import java.util.List;

/**
 * 通知中心服务接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */
public interface NotificationCenterService {

    /**
     * 发送单个通知
     * 
     * @param request 通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendNotification(NotificationRequestDTO request);

    /**
     * 批量发送通知
     * 
     * @param request 批量通知请求
     * @return 批次ID
     */
    String sendBatchNotification(BatchNotificationRequestDTO request);

    /**
     * 发送系统通知
     * 
     * @param request 系统通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendSystemNotification(SystemNotificationRequestDTO request);

    /**
     * 发送任务通知
     * 
     * @param request 任务通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendTaskNotification(TaskNotificationRequestDTO request);

    /**
     * 发送用户通知
     * 
     * @param request 用户通知请求
     * @return 通知记录DTO
     */
    NotificationRecordDTO sendUserNotification(UserNotificationRequestDTO request);

    /**
     * 重新发送失败的通知
     */
    void retryFailedNotifications();

    /**
     * 取消计划中的通知
     * 
     * @param notificationId 通知ID
     */
    void cancelScheduledNotification(Long notificationId);

    /**
     * 获取批量通知状态
     * 
     * @param batchId 批次ID
     * @return 批量通知状态
     */
    BatchNotificationStatusDTO getBatchNotificationStatus(String batchId);

    /**
     * 标记通知为已读
     * 
     * @param notificationId 通知ID
     * @param userId 用户ID
     */
    void markAsRead(Long notificationId, Long userId);

    /**
     * 批量标记通知为已读
     *
     * @param notificationIds 通知ID列表
     * @param userId 用户ID
     */
    void batchMarkAsRead(List<Long> notificationIds, Long userId);
}
