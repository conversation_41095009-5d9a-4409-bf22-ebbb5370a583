package com.whiskerguard.general.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.general.domain.NotificationRecord;
import com.whiskerguard.general.domain.NotificationSendRecord;
import com.whiskerguard.general.domain.NotificationTemplate;
import com.whiskerguard.general.domain.UserNotificationPreference;
import com.whiskerguard.general.domain.enumeration.*;
import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.repository.NotificationSendRecordRepository;
import com.whiskerguard.general.repository.NotificationTemplateRepository;
import com.whiskerguard.general.repository.UserNotificationPreferenceRepository;
import com.whiskerguard.general.service.NotificationCenterService;
import com.whiskerguard.general.service.NotificationRecordService;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.service.mapper.NotificationRecordMapper;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通知中心服务实现类
 * 
 * <AUTHOR> Yan
 * @version 1.0
 * @date 2025-06-23
 */
@Service
@Transactional
public class NotificationCenterServiceImpl implements NotificationCenterService {

    private static final Logger log = LoggerFactory.getLogger(NotificationCenterServiceImpl.class);
    
    private final NotificationRecordRepository notificationRecordRepository;
    private final NotificationSendRecordRepository notificationSendRecordRepository;
    private final NotificationTemplateRepository notificationTemplateRepository;
    private final UserNotificationPreferenceRepository userNotificationPreferenceRepository;
    private final NotificationRecordService notificationRecordService;
    private final NotificationRecordMapper notificationRecordMapper;
    private final ObjectMapper objectMapper;

    public NotificationCenterServiceImpl(
        NotificationRecordRepository notificationRecordRepository,
        NotificationSendRecordRepository notificationSendRecordRepository,
        NotificationTemplateRepository notificationTemplateRepository,
        UserNotificationPreferenceRepository userNotificationPreferenceRepository,
        NotificationRecordService notificationRecordService,
        NotificationRecordMapper notificationRecordMapper,
        ObjectMapper objectMapper
    ) {
        this.notificationRecordRepository = notificationRecordRepository;
        this.notificationSendRecordRepository = notificationSendRecordRepository;
        this.notificationTemplateRepository = notificationTemplateRepository;
        this.userNotificationPreferenceRepository = userNotificationPreferenceRepository;
        this.notificationRecordService = notificationRecordService;
        this.notificationRecordMapper = notificationRecordMapper;
        this.objectMapper = objectMapper;
    }

    @Override
    public NotificationRecordDTO sendNotification(NotificationRequestDTO request) {
        log.debug("发送通知请求: {}", request);
        
        try {
            // 创建通知记录
            NotificationRecord record = createNotificationRecord(request);
            
            // 保存通知记录
            record = notificationRecordRepository.save(record);
            
            // 异步发送通知
            // TODO: 实现异步发送逻辑
            
            log.info("通知创建成功: ID={}, 标题={}", record.getId(), record.getTitle());
            
            return notificationRecordMapper.toDto(record);
        } catch (Exception e) {
            log.error("发送通知失败: {}", request, e);
            throw new BadRequestAlertException("发送通知失败: " + e.getMessage(), "notification", "sendfailed");
        }
    }

    @Override
    public String sendBatchNotification(BatchNotificationRequestDTO request) {
        log.debug("批量发送通知请求: {}", request);
        
        try {
            // 生成批次ID
            String batchId = UUID.randomUUID().toString();
            
            // 分批处理接收者
            List<Long> recipientIds = request.getRecipientIds();
            int batchSize = request.getBatchSize();
            
            for (int i = 0; i < recipientIds.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, recipientIds.size());
                List<Long> batchRecipients = recipientIds.subList(i, endIndex);
                
                // 创建批次通知记录
                NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
                notificationRequest.setCategory(request.getCategory());
                notificationRequest.setSubType(request.getSubType());
                notificationRequest.setScope(NotificationScope.USER);
                notificationRequest.setTitle(request.getTitle());
                notificationRequest.setContent(request.getContent());
                notificationRequest.setRecipientType(RecipientType.USER);
                notificationRequest.setRecipientIds(batchRecipients);
                notificationRequest.setChannels(request.getChannels());
                notificationRequest.setPriority(request.getPriority());
                notificationRequest.setScheduledTime(request.getScheduledTime());
                notificationRequest.setBusinessId(request.getBusinessId());
                notificationRequest.setBusinessType(request.getBusinessType());
                notificationRequest.setTemplateId(request.getTemplateId());
                notificationRequest.setTemplateParams(request.getTemplateParams());
                
                // 发送批次通知
                sendNotification(notificationRequest);
            }
            
            log.info("批量通知创建成功: 批次ID={}, 总数量={}", batchId, recipientIds.size());
            
            return batchId;
        } catch (Exception e) {
            log.error("批量发送通知失败: {}", request, e);
            throw new BadRequestAlertException("批量发送通知失败: " + e.getMessage(), "notification", "batchsendfailed");
        }
    }

    @Override
    public NotificationRecordDTO sendSystemNotification(SystemNotificationRequestDTO request) {
        log.debug("发送系统通知请求: {}", request);
        
        // 转换为通用通知请求
        NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
        notificationRequest.setCategory(NotificationCategory.SYSTEM);
        notificationRequest.setSubType(request.getSubType());
        notificationRequest.setScope(request.getScope());
        notificationRequest.setTitle(request.getTitle());
        notificationRequest.setContent(request.getContent());
        notificationRequest.setRecipientType(determineRecipientType(request.getScope()));
        notificationRequest.setRecipientIds(request.getTargetIds());
        notificationRequest.setChannels(request.getChannels());
        notificationRequest.setPriority(request.getPriority());
        notificationRequest.setScheduledTime(request.getScheduledTime());
        notificationRequest.setBusinessId(request.getBusinessId());
        notificationRequest.setBusinessType(request.getBusinessType());
        notificationRequest.setTemplateId(request.getTemplateId());
        notificationRequest.setTemplateParams(request.getTemplateParams());
        
        return sendNotification(notificationRequest);
    }

    @Override
    public NotificationRecordDTO sendTaskNotification(TaskNotificationRequestDTO request) {
        log.debug("发送任务通知请求: {}", request);
        
        // 转换为通用通知请求
        NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
        notificationRequest.setCategory(NotificationCategory.TASK);
        notificationRequest.setSubType(request.getSubType());
        notificationRequest.setScope(NotificationScope.USER);
        notificationRequest.setTitle(request.getTitle());
        notificationRequest.setContent(request.getContent());
        notificationRequest.setRecipientType(RecipientType.USER);
        notificationRequest.setRecipientIds(request.getRecipients());
        notificationRequest.setChannels(request.getChannels());
        notificationRequest.setPriority(request.getPriority());
        notificationRequest.setScheduledTime(request.getScheduledTime());
        notificationRequest.setBusinessId(request.getBusinessId());
        notificationRequest.setBusinessType(request.getBusinessType());
        notificationRequest.setTemplateId(request.getTemplateId());
        notificationRequest.setTemplateParams(request.getTemplateParams());
        
        return sendNotification(notificationRequest);
    }

    @Override
    public NotificationRecordDTO sendUserNotification(UserNotificationRequestDTO request) {
        log.debug("发送用户通知请求: {}", request);
        
        // 转换为通用通知请求
        NotificationRequestDTO notificationRequest = new NotificationRequestDTO();
        notificationRequest.setCategory(NotificationCategory.USER);
        notificationRequest.setSubType(request.getSubType());
        notificationRequest.setScope(NotificationScope.USER);
        notificationRequest.setTitle(request.getTitle());
        notificationRequest.setContent(request.getContent());
        notificationRequest.setRecipientType(RecipientType.USER);
        notificationRequest.setRecipientIds(Arrays.asList(request.getUserId()));
        notificationRequest.setChannels(request.getChannels());
        notificationRequest.setPriority(request.getPriority());
        notificationRequest.setScheduledTime(request.getScheduledTime());
        notificationRequest.setBusinessId(request.getBusinessId());
        notificationRequest.setBusinessType(request.getBusinessType());
        notificationRequest.setTemplateId(request.getTemplateId());
        notificationRequest.setTemplateParams(request.getTemplateParams());
        
        return sendNotification(notificationRequest);
    }

    @Override
    public void retryFailedNotifications() {
        log.debug("重试失败的通知");
        
        // TODO: 实现重试逻辑
        // 1. 查询失败的通知记录
        // 2. 重新发送
        // 3. 更新重试次数
    }

    @Override
    public void cancelScheduledNotification(Long notificationId) {
        log.debug("取消计划通知: {}", notificationId);
        
        Optional<NotificationRecord> recordOpt = notificationRecordRepository.findById(notificationId);
        if (recordOpt.isPresent()) {
            NotificationRecord record = recordOpt.get();
            if (record.getStatus() == NotificationStatus.SCHEDULED) {
                record.setStatus(NotificationStatus.CANCELLED);
                record.setUpdatedAt(Instant.now());
                notificationRecordRepository.save(record);
                
                log.info("通知已取消: ID={}", notificationId);
            } else {
                throw new BadRequestAlertException("只能取消计划中的通知", "notification", "invalidstatus");
            }
        } else {
            throw new BadRequestAlertException("通知记录不存在", "notification", "notfound");
        }
    }

    @Override
    public BatchNotificationStatusDTO getBatchNotificationStatus(String batchId) {
        log.debug("获取批量通知状态: {}", batchId);
        
        // TODO: 实现批量通知状态查询
        BatchNotificationStatusDTO status = new BatchNotificationStatusDTO();
        status.setBatchId(batchId);
        status.setStatus("PROCESSING");
        
        return status;
    }

    @Override
    public void markAsRead(Long notificationId, Long userId) {
        log.debug("标记通知为已读: 通知ID={}, 用户ID={}", notificationId, userId);
        
        // TODO: 实现标记已读逻辑
        // 1. 查找对应的发送记录
        // 2. 更新阅读时间
        // 3. 更新状态为已读
    }

    @Override
    public void batchMarkAsRead(List<Long> notificationIds, Long userId) {
        log.debug("批量标记通知为已读: 通知IDs={}, 用户ID={}", notificationIds, userId);
        
        for (Long notificationId : notificationIds) {
            markAsRead(notificationId, userId);
        }
    }

    /**
     * 创建通知记录
     */
    private NotificationRecord createNotificationRecord(NotificationRequestDTO request) throws JsonProcessingException {
        NotificationRecord record = new NotificationRecord();
        
        // 设置基本信息
        record.setTenantId(getCurrentTenantId());
        record.setCategory(request.getCategory());
        record.setSubType(request.getSubType());
        record.setScope(request.getScope());
        record.setTitle(request.getTitle());
        record.setContent(request.getContent());
        record.setRecipientType(request.getRecipientType());
        record.setRecipientIds(objectMapper.writeValueAsString(request.getRecipientIds()));
        record.setChannels(objectMapper.writeValueAsString(request.getChannels()));
        record.setPriority(request.getPriority());
        record.setStatus(NotificationStatus.SCHEDULED);
        record.setScheduledTime(request.getScheduledTime() != null ? request.getScheduledTime() : Instant.now());
        record.setBusinessId(request.getBusinessId());
        record.setBusinessType(request.getBusinessType());
        record.setTemplateParams(request.getTemplateParams() != null ? objectMapper.writeValueAsString(request.getTemplateParams()) : null);
        record.setRetryCount(0);
        
        // 设置审计字段
        record.setVersion(1);
        record.setCreatedBy(getCurrentUserId());
        record.setCreatedAt(Instant.now());
        record.setUpdatedBy(getCurrentUserId());
        record.setUpdatedAt(Instant.now());
        record.setIsDeleted(false);
        
        return record;
    }

    /**
     * 根据通知范围确定接收者类型
     */
    private RecipientType determineRecipientType(NotificationScope scope) {
        switch (scope) {
            case GLOBAL:
            case TENANT:
                return RecipientType.ALL;
            case DEPARTMENT:
                return RecipientType.DEPARTMENT;
            case ROLE:
                return RecipientType.ROLE;
            case USER:
                return RecipientType.USER;
            default:
                return RecipientType.USER;
        }
    }

    /**
     * 获取当前租户ID
     * TODO: 实现租户上下文获取
     */
    private Long getCurrentTenantId() {
        // 临时返回默认值，实际应该从租户上下文获取
        return 1L;
    }

    /**
     * 获取当前用户ID
     * TODO: 实现用户上下文获取
     */
    private String getCurrentUserId() {
        // 临时返回默认值，实际应该从安全上下文获取
        return "system";
    }
}
