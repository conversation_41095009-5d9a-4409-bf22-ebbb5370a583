package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.repository.NotificationRecordRepository;
import com.whiskerguard.general.service.NotificationRecordService;
import com.whiskerguard.general.service.dto.NotificationRecordDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.general.domain.NotificationRecord}.
 */
@RestController
@RequestMapping("/api/notification-records")
public class NotificationRecordResource {

    private static final Logger LOG = LoggerFactory.getLogger(NotificationRecordResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceNotificationRecord";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NotificationRecordService notificationRecordService;

    private final NotificationRecordRepository notificationRecordRepository;

    public NotificationRecordResource(
        NotificationRecordService notificationRecordService,
        NotificationRecordRepository notificationRecordRepository
    ) {
        this.notificationRecordService = notificationRecordService;
        this.notificationRecordRepository = notificationRecordRepository;
    }

    /**
     * {@code POST  /notification-records} : Create a new notificationRecord.
     *
     * @param notificationRecordDTO the notificationRecordDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new notificationRecordDTO, or with status {@code 400 (Bad Request)} if the notificationRecord has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<NotificationRecordDTO> createNotificationRecord(@Valid @RequestBody NotificationRecordDTO notificationRecordDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save NotificationRecord : {}", notificationRecordDTO);
        if (notificationRecordDTO.getId() != null) {
            throw new BadRequestAlertException("A new notificationRecord cannot already have an ID", ENTITY_NAME, "idexists");
        }
        notificationRecordDTO = notificationRecordService.save(notificationRecordDTO);
        return ResponseEntity.created(new URI("/api/notification-records/" + notificationRecordDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, notificationRecordDTO.getId().toString()))
            .body(notificationRecordDTO);
    }

    /**
     * {@code PUT  /notification-records/:id} : Updates an existing notificationRecord.
     *
     * @param id the id of the notificationRecordDTO to save.
     * @param notificationRecordDTO the notificationRecordDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated notificationRecordDTO,
     * or with status {@code 400 (Bad Request)} if the notificationRecordDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the notificationRecordDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<NotificationRecordDTO> updateNotificationRecord(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NotificationRecordDTO notificationRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NotificationRecord : {}, {}", id, notificationRecordDTO);
        if (notificationRecordDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, notificationRecordDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!notificationRecordRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        notificationRecordDTO = notificationRecordService.update(notificationRecordDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, notificationRecordDTO.getId().toString()))
            .body(notificationRecordDTO);
    }

    /**
     * {@code PATCH  /notification-records/:id} : Partial updates given fields of an existing notificationRecord, field will ignore if it is null
     *
     * @param id the id of the notificationRecordDTO to save.
     * @param notificationRecordDTO the notificationRecordDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated notificationRecordDTO,
     * or with status {@code 400 (Bad Request)} if the notificationRecordDTO is not valid,
     * or with status {@code 404 (Not Found)} if the notificationRecordDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the notificationRecordDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NotificationRecordDTO> partialUpdateNotificationRecord(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody NotificationRecordDTO notificationRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NotificationRecord partially : {}, {}", id, notificationRecordDTO);
        if (notificationRecordDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, notificationRecordDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!notificationRecordRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<NotificationRecordDTO> result = notificationRecordService.partialUpdate(notificationRecordDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, notificationRecordDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /notification-records} : get all the notificationRecords.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of notificationRecords in body.
     */
    @GetMapping("")
    public ResponseEntity<List<NotificationRecordDTO>> getAllNotificationRecords(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of NotificationRecords");
        Page<NotificationRecordDTO> page = notificationRecordService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /notification-records/:id} : get the "id" notificationRecord.
     *
     * @param id the id of the notificationRecordDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the notificationRecordDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<NotificationRecordDTO> getNotificationRecord(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NotificationRecord : {}", id);
        Optional<NotificationRecordDTO> notificationRecordDTO = notificationRecordService.findOne(id);
        return ResponseUtil.wrapOrNotFound(notificationRecordDTO);
    }

    /**
     * {@code DELETE  /notification-records/:id} : delete the "id" notificationRecord.
     *
     * @param id the id of the notificationRecordDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNotificationRecord(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NotificationRecord : {}", id);
        notificationRecordService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
