package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.service.NotificationCenterService;
import com.whiskerguard.general.service.dto.*;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知中心REST控制器
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */
@RestController
@RequestMapping("/api/notification-center")
@Tag(name = "notification-center", description = "通知中心 API")
public class NotificationCenterResource {

    private static final Logger log = LoggerFactory.getLogger(NotificationCenterResource.class);

    private static final String ENTITY_NAME = "notificationCenter";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NotificationCenterService notificationCenterService;

    public NotificationCenterResource(NotificationCenterService notificationCenterService) {
        this.notificationCenterService = notificationCenterService;
    }

    /**
     * 发送单个通知
     */
    @PostMapping("/send")
    @Operation(summary = "发送通知", description = "发送单个通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<NotificationRecordDTO> sendNotification(
        @Valid @RequestBody NotificationRequestDTO request) {
        
        log.debug("REST request to send notification: {}", request);
        
        if (request.getCategory() == null) {
            throw new BadRequestAlertException("通知分类不能为空", ENTITY_NAME, "categorynull");
        }
        
        if (request.getSubType() == null) {
            throw new BadRequestAlertException("通知子类型不能为空", ENTITY_NAME, "subtypenull");
        }
        
        NotificationRecordDTO result = notificationCenterService.sendNotification(request);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 批量发送通知
     */
    @PostMapping("/batch-send")
    @Operation(summary = "批量发送通知", description = "批量发送通知给多个接收者")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "批量发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<BatchNotificationResponseDTO> sendBatchNotification(
        @Valid @RequestBody BatchNotificationRequestDTO request) {
        
        log.debug("REST request to send batch notification: {}", request);
        
        if (request.getRecipientIds() == null || request.getRecipientIds().isEmpty()) {
            throw new BadRequestAlertException("接收者列表不能为空", ENTITY_NAME, "recipientsnull");
        }
        
        String batchId = notificationCenterService.sendBatchNotification(request);
        
        BatchNotificationResponseDTO response = new BatchNotificationResponseDTO();
        response.setBatchId(batchId);
        response.setStatus("PROCESSING");
        response.setMessage("批量通知已提交处理");
        response.setTotalCount(request.getRecipientIds().size());
        
        return ResponseEntity.ok(response);
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/system")
    @Operation(summary = "发送系统通知", description = "发送系统级通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<NotificationRecordDTO> sendSystemNotification(
        @Valid @RequestBody SystemNotificationRequestDTO request) {
        
        log.debug("REST request to send system notification: {}", request);
        
        NotificationRecordDTO result = notificationCenterService.sendSystemNotification(request);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 发送任务通知
     */
    @PostMapping("/task")
    @Operation(summary = "发送任务通知", description = "发送任务相关通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<NotificationRecordDTO> sendTaskNotification(
        @Valid @RequestBody TaskNotificationRequestDTO request) {
        
        log.debug("REST request to send task notification: {}", request);
        
        if (request.getBusinessId() == null || request.getBusinessId().trim().isEmpty()) {
            throw new BadRequestAlertException("业务ID不能为空", ENTITY_NAME, "businessidnull");
        }
        
        NotificationRecordDTO result = notificationCenterService.sendTaskNotification(request);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 发送用户通知
     */
    @PostMapping("/user")
    @Operation(summary = "发送用户通知", description = "发送用户相关通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<NotificationRecordDTO> sendUserNotification(
        @Valid @RequestBody UserNotificationRequestDTO request) {
        
        log.debug("REST request to send user notification: {}", request);
        
        if (request.getUserId() == null) {
            throw new BadRequestAlertException("用户ID不能为空", ENTITY_NAME, "useridnull");
        }
        
        NotificationRecordDTO result = notificationCenterService.sendUserNotification(request);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 取消计划通知
     */
    @PutMapping("/{id}/cancel")
    @Operation(summary = "取消计划通知", description = "取消指定的计划通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "取消成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "通知不存在")
    })
    public ResponseEntity<Void> cancelScheduledNotification(@PathVariable Long id) {
        log.debug("REST request to cancel scheduled notification: {}", id);
        
        notificationCenterService.cancelScheduledNotification(id);
        
        return ResponseEntity.ok().build();
    }

    /**
     * 重试失败通知
     */
    @PostMapping("/retry-failed")
    @Operation(summary = "重试失败通知", description = "重新发送失败的通知")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "重试成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public ResponseEntity<Void> retryFailedNotifications() {
        log.debug("REST request to retry failed notifications");
        
        notificationCenterService.retryFailedNotifications();
        
        return ResponseEntity.ok().build();
    }

    /**
     * 获取批量通知状态
     */
    @GetMapping("/batch/{batchId}/status")
    @Operation(summary = "获取批量通知状态", description = "获取批量通知的处理状态")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "404", description = "批次不存在")
    })
    public ResponseEntity<BatchNotificationStatusDTO> getBatchNotificationStatus(@PathVariable String batchId) {
        log.debug("REST request to get batch notification status: {}", batchId);
        
        BatchNotificationStatusDTO status = notificationCenterService.getBatchNotificationStatus(batchId);
        
        return ResponseEntity.ok(status);
    }

    /**
     * 标记通知为已读
     */
    @PutMapping("/{id}/read")
    @Operation(summary = "标记通知为已读", description = "标记指定通知为已读状态")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "标记成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "通知不存在")
    })
    public ResponseEntity<Void> markAsRead(
        @PathVariable Long id,
        @RequestParam Long userId) {
        
        log.debug("REST request to mark notification as read: notificationId={}, userId={}", id, userId);
        
        notificationCenterService.markAsRead(id, userId);
        
        return ResponseEntity.ok().build();
    }

    /**
     * 批量标记通知为已读
     */
    @PutMapping("/batch-read")
    @Operation(summary = "批量标记通知为已读", description = "批量标记多个通知为已读状态")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "批量标记成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误")
    })
    public ResponseEntity<Void> batchMarkAsRead(
        @RequestBody List<Long> notificationIds,
        @RequestParam Long userId) {
        
        log.debug("REST request to batch mark notifications as read: notificationIds={}, userId={}", notificationIds, userId);
        
        if (notificationIds == null || notificationIds.isEmpty()) {
            throw new BadRequestAlertException("通知ID列表不能为空", ENTITY_NAME, "notificationidsnull");
        }
        
        notificationCenterService.batchMarkAsRead(notificationIds, userId);
        
        return ResponseEntity.ok().build();
    }
}
