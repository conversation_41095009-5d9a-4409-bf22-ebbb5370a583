package com.whiskerguard.general.web.rest;

import com.whiskerguard.general.repository.UserNotificationPreferenceRepository;
import com.whiskerguard.general.service.UserNotificationPreferenceService;
import com.whiskerguard.general.service.dto.UserNotificationPreferenceDTO;
import com.whiskerguard.general.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.general.domain.UserNotificationPreference}.
 */
@RestController
@RequestMapping("/api/user-notification-preferences")
public class UserNotificationPreferenceResource {

    private static final Logger LOG = LoggerFactory.getLogger(UserNotificationPreferenceResource.class);

    private static final String ENTITY_NAME = "whiskerguardGeneralServiceUserNotificationPreference";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final UserNotificationPreferenceService userNotificationPreferenceService;

    private final UserNotificationPreferenceRepository userNotificationPreferenceRepository;

    public UserNotificationPreferenceResource(
        UserNotificationPreferenceService userNotificationPreferenceService,
        UserNotificationPreferenceRepository userNotificationPreferenceRepository
    ) {
        this.userNotificationPreferenceService = userNotificationPreferenceService;
        this.userNotificationPreferenceRepository = userNotificationPreferenceRepository;
    }

    /**
     * {@code POST  /user-notification-preferences} : Create a new userNotificationPreference.
     *
     * @param userNotificationPreferenceDTO the userNotificationPreferenceDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new userNotificationPreferenceDTO, or with status {@code 400 (Bad Request)} if the userNotificationPreference has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<UserNotificationPreferenceDTO> createUserNotificationPreference(
        @Valid @RequestBody UserNotificationPreferenceDTO userNotificationPreferenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save UserNotificationPreference : {}", userNotificationPreferenceDTO);
        if (userNotificationPreferenceDTO.getId() != null) {
            throw new BadRequestAlertException("A new userNotificationPreference cannot already have an ID", ENTITY_NAME, "idexists");
        }
        userNotificationPreferenceDTO = userNotificationPreferenceService.save(userNotificationPreferenceDTO);
        return ResponseEntity.created(new URI("/api/user-notification-preferences/" + userNotificationPreferenceDTO.getId()))
            .headers(
                HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, userNotificationPreferenceDTO.getId().toString())
            )
            .body(userNotificationPreferenceDTO);
    }

    /**
     * {@code PUT  /user-notification-preferences/:id} : Updates an existing userNotificationPreference.
     *
     * @param id the id of the userNotificationPreferenceDTO to save.
     * @param userNotificationPreferenceDTO the userNotificationPreferenceDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated userNotificationPreferenceDTO,
     * or with status {@code 400 (Bad Request)} if the userNotificationPreferenceDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the userNotificationPreferenceDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<UserNotificationPreferenceDTO> updateUserNotificationPreference(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody UserNotificationPreferenceDTO userNotificationPreferenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update UserNotificationPreference : {}, {}", id, userNotificationPreferenceDTO);
        if (userNotificationPreferenceDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, userNotificationPreferenceDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!userNotificationPreferenceRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        userNotificationPreferenceDTO = userNotificationPreferenceService.update(userNotificationPreferenceDTO);
        return ResponseEntity.ok()
            .headers(
                HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, userNotificationPreferenceDTO.getId().toString())
            )
            .body(userNotificationPreferenceDTO);
    }

    /**
     * {@code PATCH  /user-notification-preferences/:id} : Partial updates given fields of an existing userNotificationPreference, field will ignore if it is null
     *
     * @param id the id of the userNotificationPreferenceDTO to save.
     * @param userNotificationPreferenceDTO the userNotificationPreferenceDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated userNotificationPreferenceDTO,
     * or with status {@code 400 (Bad Request)} if the userNotificationPreferenceDTO is not valid,
     * or with status {@code 404 (Not Found)} if the userNotificationPreferenceDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the userNotificationPreferenceDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<UserNotificationPreferenceDTO> partialUpdateUserNotificationPreference(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody UserNotificationPreferenceDTO userNotificationPreferenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update UserNotificationPreference partially : {}, {}", id, userNotificationPreferenceDTO);
        if (userNotificationPreferenceDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, userNotificationPreferenceDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!userNotificationPreferenceRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<UserNotificationPreferenceDTO> result = userNotificationPreferenceService.partialUpdate(userNotificationPreferenceDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, userNotificationPreferenceDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /user-notification-preferences} : get all the userNotificationPreferences.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of userNotificationPreferences in body.
     */
    @GetMapping("")
    public ResponseEntity<List<UserNotificationPreferenceDTO>> getAllUserNotificationPreferences(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of UserNotificationPreferences");
        Page<UserNotificationPreferenceDTO> page = userNotificationPreferenceService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /user-notification-preferences/:id} : get the "id" userNotificationPreference.
     *
     * @param id the id of the userNotificationPreferenceDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the userNotificationPreferenceDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<UserNotificationPreferenceDTO> getUserNotificationPreference(@PathVariable("id") Long id) {
        LOG.debug("REST request to get UserNotificationPreference : {}", id);
        Optional<UserNotificationPreferenceDTO> userNotificationPreferenceDTO = userNotificationPreferenceService.findOne(id);
        return ResponseUtil.wrapOrNotFound(userNotificationPreferenceDTO);
    }

    /**
     * {@code DELETE  /user-notification-preferences/:id} : delete the "id" userNotificationPreference.
     *
     * @param id the id of the userNotificationPreferenceDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUserNotificationPreference(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete UserNotificationPreference : {}", id);
        userNotificationPreferenceService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}
