package com.whiskerguard.general.domain;

import com.whiskerguard.general.domain.enumeration.NotificationCategory;
import com.whiskerguard.general.domain.enumeration.NotificationSubType;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * 存储用户的通知偏好设置
 */
@Entity
@Table(name = "user_notification_preference")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class UserNotificationPreference implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @NotNull
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    /**
     * 租户ID（0 = 平台级）
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 用户ID
     */
    @NotNull
    @Column(name = "user_id", nullable = false)
    private Long userId;

    /**
     * 通知分类
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "category", nullable = false)
    private NotificationCategory category;

    /**
     * 通知子类型
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "sub_type")
    private NotificationSubType subType;

    /**
     * 启用的渠道(JSON格式)
     */
    @Size(max = 200)
    @Column(name = "enabled_channels", length = 200)
    private String enabledChannels;

    /**
     * 免打扰开始时间
     */
    @Size(max = 5)
    @Column(name = "quiet_hours_start", length = 5)
    private String quietHoursStart;

    /**
     * 免打扰结束时间
     */
    @Size(max = 5)
    @Column(name = "quiet_hours_end", length = 5)
    private String quietHoursEnd;

    /**
     * 是否启用
     */
    @NotNull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 50)
    @Column(name = "updated_by", length = 50)
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public UserNotificationPreference id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public UserNotificationPreference tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getUserId() {
        return this.userId;
    }

    public UserNotificationPreference userId(Long userId) {
        this.setUserId(userId);
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public NotificationCategory getCategory() {
        return this.category;
    }

    public UserNotificationPreference category(NotificationCategory category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(NotificationCategory category) {
        this.category = category;
    }

    public NotificationSubType getSubType() {
        return this.subType;
    }

    public UserNotificationPreference subType(NotificationSubType subType) {
        this.setSubType(subType);
        return this;
    }

    public void setSubType(NotificationSubType subType) {
        this.subType = subType;
    }

    public String getEnabledChannels() {
        return this.enabledChannels;
    }

    public UserNotificationPreference enabledChannels(String enabledChannels) {
        this.setEnabledChannels(enabledChannels);
        return this;
    }

    public void setEnabledChannels(String enabledChannels) {
        this.enabledChannels = enabledChannels;
    }

    public String getQuietHoursStart() {
        return this.quietHoursStart;
    }

    public UserNotificationPreference quietHoursStart(String quietHoursStart) {
        this.setQuietHoursStart(quietHoursStart);
        return this;
    }

    public void setQuietHoursStart(String quietHoursStart) {
        this.quietHoursStart = quietHoursStart;
    }

    public String getQuietHoursEnd() {
        return this.quietHoursEnd;
    }

    public UserNotificationPreference quietHoursEnd(String quietHoursEnd) {
        this.setQuietHoursEnd(quietHoursEnd);
        return this;
    }

    public void setQuietHoursEnd(String quietHoursEnd) {
        this.quietHoursEnd = quietHoursEnd;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public UserNotificationPreference enabled(Boolean enabled) {
        this.setEnabled(enabled);
        return this;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Integer getVersion() {
        return this.version;
    }

    public UserNotificationPreference version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public UserNotificationPreference createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public UserNotificationPreference createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public UserNotificationPreference updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public UserNotificationPreference updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public UserNotificationPreference isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UserNotificationPreference)) {
            return false;
        }
        return getId() != null && getId().equals(((UserNotificationPreference) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "UserNotificationPreference{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", userId=" + getUserId() +
            ", category='" + getCategory() + "'" +
            ", subType='" + getSubType() + "'" +
            ", enabledChannels='" + getEnabledChannels() + "'" +
            ", quietHoursStart='" + getQuietHoursStart() + "'" +
            ", quietHoursEnd='" + getQuietHoursEnd() + "'" +
            ", enabled='" + getEnabled() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
