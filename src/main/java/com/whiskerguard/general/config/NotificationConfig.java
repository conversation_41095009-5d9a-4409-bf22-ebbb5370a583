package com.whiskerguard.general.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 通知模块配置类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */
@Configuration
@ConfigurationProperties(prefix = "application.notification")
public class NotificationConfig {

    /**
     * 通知中心配置
     */
    private Center center = new Center();

    /**
     * 模板配置
     */
    private Template template = new Template();

    /**
     * 用户偏好配置
     */
    private Preference preference = new Preference();

    /**
     * 批量处理配置
     */
    private Batch batch = new Batch();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    // Getters and Setters
    public Center getCenter() {
        return center;
    }

    public void setCenter(Center center) {
        this.center = center;
    }

    public Template getTemplate() {
        return template;
    }

    public void setTemplate(Template template) {
        this.template = template;
    }

    public Preference getPreference() {
        return preference;
    }

    public void setPreference(Preference preference) {
        this.preference = preference;
    }

    public Batch getBatch() {
        return batch;
    }

    public void setBatch(Batch batch) {
        this.batch = batch;
    }

    public Retry getRetry() {
        return retry;
    }

    public void setRetry(Retry retry) {
        this.retry = retry;
    }

    /**
     * 通知中心配置
     */
    public static class Center {
        /**
         * 是否启用通知中心
         */
        private boolean enabled = true;

        /**
         * 是否启用异步发送
         */
        private boolean asyncEnabled = true;

        /**
         * 默认批次大小
         */
        private int defaultBatchSize = 100;

        /**
         * 最大批次大小
         */
        private int maxBatchSize = 1000;

        // Getters and Setters
        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public boolean isAsyncEnabled() {
            return asyncEnabled;
        }

        public void setAsyncEnabled(boolean asyncEnabled) {
            this.asyncEnabled = asyncEnabled;
        }

        public int getDefaultBatchSize() {
            return defaultBatchSize;
        }

        public void setDefaultBatchSize(int defaultBatchSize) {
            this.defaultBatchSize = defaultBatchSize;
        }

        public int getMaxBatchSize() {
            return maxBatchSize;
        }

        public void setMaxBatchSize(int maxBatchSize) {
            this.maxBatchSize = maxBatchSize;
        }
    }

    /**
     * 模板配置
     */
    public static class Template {
        /**
         * 是否启用缓存
         */
        private boolean cacheEnabled = true;

        /**
         * 缓存TTL（分钟）
         */
        private int cacheTtlMinutes = 30;

        /**
         * 默认语言
         */
        private String defaultLanguage = "zh-CN";

        // Getters and Setters
        public boolean isCacheEnabled() {
            return cacheEnabled;
        }

        public void setCacheEnabled(boolean cacheEnabled) {
            this.cacheEnabled = cacheEnabled;
        }

        public int getCacheTtlMinutes() {
            return cacheTtlMinutes;
        }

        public void setCacheTtlMinutes(int cacheTtlMinutes) {
            this.cacheTtlMinutes = cacheTtlMinutes;
        }

        public String getDefaultLanguage() {
            return defaultLanguage;
        }

        public void setDefaultLanguage(String defaultLanguage) {
            this.defaultLanguage = defaultLanguage;
        }
    }

    /**
     * 用户偏好配置
     */
    public static class Preference {
        /**
         * 默认启用的渠道
         */
        private String[] defaultChannels = {"EMAIL", "PUSH"};

        /**
         * 是否启用免打扰时间
         */
        private boolean quietHoursEnabled = true;

        /**
         * 默认免打扰开始时间
         */
        private String defaultQuietHoursStart = "22:00";

        /**
         * 默认免打扰结束时间
         */
        private String defaultQuietHoursEnd = "08:00";

        // Getters and Setters
        public String[] getDefaultChannels() {
            return defaultChannels;
        }

        public void setDefaultChannels(String[] defaultChannels) {
            this.defaultChannels = defaultChannels;
        }

        public boolean isQuietHoursEnabled() {
            return quietHoursEnabled;
        }

        public void setQuietHoursEnabled(boolean quietHoursEnabled) {
            this.quietHoursEnabled = quietHoursEnabled;
        }

        public String getDefaultQuietHoursStart() {
            return defaultQuietHoursStart;
        }

        public void setDefaultQuietHoursStart(String defaultQuietHoursStart) {
            this.defaultQuietHoursStart = defaultQuietHoursStart;
        }

        public String getDefaultQuietHoursEnd() {
            return defaultQuietHoursEnd;
        }

        public void setDefaultQuietHoursEnd(String defaultQuietHoursEnd) {
            this.defaultQuietHoursEnd = defaultQuietHoursEnd;
        }
    }

    /**
     * 批量处理配置
     */
    public static class Batch {
        /**
         * 批量处理大小
         */
        private int size = 100;

        /**
         * 线程池大小
         */
        private int threadPoolSize = 10;

        /**
         * 队列容量
         */
        private int queueCapacity = 1000;

        // Getters and Setters
        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getThreadPoolSize() {
            return threadPoolSize;
        }

        public void setThreadPoolSize(int threadPoolSize) {
            this.threadPoolSize = threadPoolSize;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }
    }

    /**
     * 重试配置
     */
    public static class Retry {
        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 重试延迟（秒）
         */
        private int delaySeconds = 60;

        /**
         * 重试间隔倍数
         */
        private double backoffMultiplier = 2.0;

        /**
         * 最大重试间隔（秒）
         */
        private int maxDelaySeconds = 3600;

        // Getters and Setters
        public int getMaxAttempts() {
            return maxAttempts;
        }

        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public int getDelaySeconds() {
            return delaySeconds;
        }

        public void setDelaySeconds(int delaySeconds) {
            this.delaySeconds = delaySeconds;
        }

        public double getBackoffMultiplier() {
            return backoffMultiplier;
        }

        public void setBackoffMultiplier(double backoffMultiplier) {
            this.backoffMultiplier = backoffMultiplier;
        }

        public int getMaxDelaySeconds() {
            return maxDelaySeconds;
        }

        public void setMaxDelaySeconds(int maxDelaySeconds) {
            this.maxDelaySeconds = maxDelaySeconds;
        }
    }
}
