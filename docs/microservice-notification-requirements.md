# 微服务通知需求分析

## 1. 企业组织架构服务 (whiskerguard-org-service)

### 通知场景
- **组织架构变更**: 部门新增、删除、调整
- **人员变动**: 员工入职、离职、调岗
- **权限变更**: 角色权限调整通知

### 实现方案
```java
// 组织架构变更通知
NotificationSubType.ORG_STRUCTURE_CHANGED
// 模板参数: {departmentName, changeType, operator, effectiveDate}

// 人员变动通知
NotificationSubType.PERSONNEL_CHANGE
// 模板参数: {employeeName, changeType, fromDept, toDept, effectiveDate}
```

## 2. 用户认证服务 (whiskerguard-auth-service)

### 通知场景
- **登录异常**: 异地登录、多次失败登录
- **密码安全**: 密码修改、密码即将过期
- **账户安全**: 账户锁定、解锁

### 实现方案
```java
// 已实现
NotificationSubType.USER_LOGIN
NotificationSubType.PASSWORD_CHANGED
// 新增
NotificationSubType.ACCOUNT_LOCKED
NotificationSubType.PASSWORD_EXPIRING
```

## 3. 企业三张清单服务 (whiskerguard-compliance-list-service)

### 通知场景
- **清单更新**: 权力清单、责任清单、负面清单更新
- **合规检查**: 定期合规检查提醒
- **清单到期**: 清单有效期提醒

### 实现方案
```java
NotificationSubType.COMPLIANCE_LIST_UPDATED
NotificationSubType.COMPLIANCE_CHECK_REMINDER
NotificationSubType.COMPLIANCE_LIST_EXPIRING
```

## 4. 审批流程服务 (whiskerguard-approval-service)

### 通知场景
- **审批任务**: 待审批、审批超时
- **审批结果**: 审批通过、拒绝、撤回
- **流程变更**: 审批流程调整

### 实现方案
```java
// 已实现
NotificationSubType.APPROVAL_PENDING
NotificationSubType.APPROVAL_APPROVED
NotificationSubType.APPROVAL_REJECTED
// 新增
NotificationSubType.APPROVAL_TIMEOUT
NotificationSubType.APPROVAL_WITHDRAWN
```

## 5. 法律法规管理服务 (whiskerguard-regulatory-service)

### 通知场景
- **法规更新**: 新法规发布、现有法规修订
- **合规截止**: 合规要求截止日期提醒
- **影响分析**: 法规变更对企业的影响分析

### 实现方案
```java
NotificationSubType.REGULATORY_UPDATE
NotificationSubType.COMPLIANCE_DEADLINE
NotificationSubType.REGULATORY_IMPACT_ANALYSIS
```

## 6. 订单管理服务 (whiskerguard-order-service)

### 通知场景
- **订单状态**: 订单创建、支付、发货、完成
- **订单异常**: 支付失败、订单取消
- **续费提醒**: 服务即将到期

### 实现方案
```java
// 已实现
NotificationSubType.ORDER_STATUS_CHANGED
// 新增
NotificationSubType.ORDER_PAYMENT_FAILED
NotificationSubType.SERVICE_RENEWAL_REMINDER
```

## 7. License 管理服务 (whiskerguard-license-service)

### 通知场景
- **许可证到期**: 到期前30天、15天、7天、1天提醒
- **许可证更新**: 许可证续期、升级
- **使用量告警**: 许可证使用量达到阈值

### 实现方案
```java
// 已实现
NotificationSubType.LICENSE_EXPIRED
// 新增
NotificationSubType.LICENSE_EXPIRING
NotificationSubType.LICENSE_USAGE_ALERT
NotificationSubType.LICENSE_RENEWED
```

## 8. 合规培训服务 (whiskerguard-training-service)

### 通知场景
- **培训提醒**: 培训开始前提醒
- **培训完成**: 培训完成确认
- **考试通知**: 考试安排、成绩发布

### 实现方案
```java
// 已实现
NotificationSubType.TRAINING_REMINDER
// 新增
NotificationSubType.TRAINING_COMPLETED
NotificationSubType.EXAM_SCHEDULED
NotificationSubType.EXAM_RESULT_PUBLISHED
```

## 9. 合规驾驶舱 (whiskerguard-dashboard-service)

### 通知场景
- **数据异常**: 关键指标异常告警
- **报表生成**: 定期报表生成完成
- **阈值告警**: 指标超过预设阈值

### 实现方案
```java
// 已实现
NotificationSubType.DASHBOARD_ALERT
// 新增
NotificationSubType.DATA_ANOMALY_DETECTED
NotificationSubType.REPORT_GENERATED
NotificationSubType.THRESHOLD_EXCEEDED
```

## 10. AI 工具管理服务 (whiskerguard-ai-service)

### 通知场景
- **分析完成**: AI分析任务完成
- **模型更新**: AI模型版本更新
- **异常检测**: AI检测到异常情况

### 实现方案
```java
// 已实现
NotificationSubType.AI_ANALYSIS_COMPLETED
// 新增
NotificationSubType.AI_MODEL_UPDATED
NotificationSubType.AI_ANOMALY_DETECTED
```

## 11. 合同管理服务 (whiskerguard-contract-service)

### 通知场景
- **合同到期**: 合同到期前提醒
- **合同审批**: 合同审批流程通知
- **合同变更**: 合同修订、续签

### 实现方案
```java
// 已实现
NotificationSubType.CONTRACT_EXPIRING
// 新增
NotificationSubType.CONTRACT_APPROVAL_PENDING
NotificationSubType.CONTRACT_RENEWED
NotificationSubType.CONTRACT_AMENDED
```

## 12. 平台运营管理服务 (whiskerguard-admin-service)

### 通知场景
- **系统维护**: 系统维护通知
- **用户管理**: 用户账户操作
- **系统监控**: 系统性能告警

### 实现方案
```java
// 已实现
NotificationSubType.SYSTEM_MAINTENANCE
NotificationSubType.SYSTEM_ANNOUNCEMENT
// 新增
NotificationSubType.USER_ACCOUNT_OPERATION
NotificationSubType.SYSTEM_PERFORMANCE_ALERT
```

## 13. 违规举报服务 (whiskerguard-violation-service)

### 通知场景
- **举报接收**: 新举报提交
- **处理进度**: 举报处理状态更新
- **处理结果**: 举报处理完成

### 实现方案
```java
// 已实现
NotificationSubType.VIOLATION_REPORTED
// 新增
NotificationSubType.VIOLATION_PROCESSING
NotificationSubType.VIOLATION_RESOLVED
```

## 14. 风险预警服务 (whiskerguard-risk-service)

### 通知场景
- **风险识别**: 新风险识别
- **风险等级**: 风险等级变更
- **风险处置**: 风险处置完成

### 实现方案
```java
// 已实现
NotificationSubType.RISK_ALERT
// 新增
NotificationSubType.RISK_LEVEL_CHANGED
NotificationSubType.RISK_MITIGATED
```

## 15. ESG 合规服务 (whiskerguard-esg-service)

### 通知场景
- **ESG评估**: ESG评估提醒
- **指标更新**: ESG指标数据更新
- **报告发布**: ESG报告发布

### 实现方案
```java
// 已实现
NotificationSubType.ESG_REMINDER
// 新增
NotificationSubType.ESG_ASSESSMENT_DUE
NotificationSubType.ESG_METRICS_UPDATED
NotificationSubType.ESG_REPORT_PUBLISHED
```

## 16. 财务合规服务 (whiskerguard-financial-service)

### 通知场景
- **财务审计**: 财务审计提醒
- **合规检查**: 财务合规检查
- **异常告警**: 财务数据异常

### 实现方案
```java
// 已实现
NotificationSubType.FINANCIAL_ALERT
// 新增
NotificationSubType.FINANCIAL_AUDIT_DUE
NotificationSubType.FINANCIAL_COMPLIANCE_CHECK
NotificationSubType.FINANCIAL_ANOMALY
```

## 通知渠道策略

### 按优先级分配渠道
- **URGENT**: SMS + EMAIL + PUSH + 站内消息
- **HIGH**: EMAIL + PUSH + 站内消息
- **NORMAL**: EMAIL + 站内消息
- **LOW**: 站内消息

### 按通知类型分配渠道
- **安全相关**: SMS + EMAIL
- **任务相关**: EMAIL + PUSH
- **系统公告**: EMAIL + 站内消息
- **用户操作**: 站内消息
