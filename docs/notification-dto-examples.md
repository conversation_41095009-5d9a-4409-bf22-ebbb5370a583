# 通知模块 DTO 示例

## 1. 用户ID类型修正说明

根据您的反馈，所有涉及用户ID的字段都已从 `String` 类型修正为 `Long` 类型。

## 2. 核心 DTO 定义

### 2.1 用户通知偏好 DTO
```java
public class UserNotificationPreferenceDTO {
    private Long id;
    private Long tenantId;
    private Long userId;  // 修正：从 String 改为 Long
    private NotificationCategory category;
    private NotificationSubType subType;
    private String enabledChannels;
    private String quietHoursStart;
    private String quietHoursEnd;
    private Boolean enabled;
    private Integer version;
    private String createdBy;
    private Instant createdAt;
    private String updatedBy;
    private Instant updatedAt;
    private Boolean isDeleted;
    
    // getters and setters...
}
```

### 2.2 通知发送记录 DTO
```java
public class NotificationSendRecordDTO {
    private Long id;
    private Long tenantId;
    private Long notificationId;
    private Long recipientId;  // 修正：从 String 改为 Long
    private RecipientType recipientType;
    private NotificationType channel;
    private SendStatus status;
    private Instant sentTime;
    private Instant readTime;
    private String errorMessage;
    private String externalId;
    private Integer version;
    private String createdBy;
    private Instant createdAt;
    private String updatedBy;
    private Instant updatedAt;
    private Boolean isDeleted;
    
    // 关联数据
    private NotificationRecordDTO notification;
    
    // getters and setters...
}
```

### 2.3 通知请求 DTO
```java
public class NotificationRequestDTO {
    @NotNull
    private NotificationCategory category;
    
    @NotNull
    private NotificationSubType subType;
    
    @NotNull
    private NotificationScope scope;
    
    @NotBlank
    @Size(max = 200)
    private String title;
    
    private String content;
    
    @NotNull
    private RecipientType recipientType;
    
    private List<Long> recipientIds;  // 修正：从 String 改为 Long
    
    private List<NotificationType> channels;
    
    @NotNull
    private NotificationPriority priority;
    
    private Instant scheduledTime;
    
    private String businessId;
    
    private String businessType;
    
    private Long templateId;
    
    private Map<String, Object> templateParams;
    
    // getters and setters...
}
```

### 2.4 用户通知请求 DTO
```java
public class UserNotificationRequestDTO {
    @NotNull
    private Long userId;  // 修正：从 String 改为 Long
    
    @NotNull
    private NotificationSubType subType;
    
    @NotBlank
    @Size(max = 200)
    private String title;
    
    private String content;
    
    private List<NotificationType> channels;
    
    @NotNull
    private NotificationPriority priority;
    
    private Instant scheduledTime;
    
    private String businessId;
    
    private String businessType;
    
    private Long templateId;
    
    private Map<String, Object> templateParams;
    
    // getters and setters...
}
```

### 2.5 批量通知请求 DTO
```java
public class BatchNotificationRequestDTO {
    @NotNull
    private NotificationCategory category;
    
    @NotNull
    private NotificationSubType subType;
    
    @NotBlank
    private String title;
    
    private String content;
    
    @NotEmpty
    private List<Long> recipientIds;  // 修正：从 String 改为 Long
    
    private List<NotificationType> channels;
    
    @NotNull
    private NotificationPriority priority;
    
    private Instant scheduledTime;
    
    private String businessId;
    
    private String businessType;
    
    private Long templateId;
    
    private Map<String, Object> templateParams;
    
    @Min(1)
    @Max(1000)
    private Integer batchSize = 100;
    
    // getters and setters...
}
```

## 3. Repository 查询方法更新

### 3.1 用户通知偏好 Repository
```java
@Repository
public interface UserNotificationPreferenceRepository extends JpaRepository<UserNotificationPreference, Long>, JpaSpecificationExecutor<UserNotificationPreference> {
    
    List<UserNotificationPreference> findByUserIdAndTenantIdAndEnabledTrue(Long userId, Long tenantId);
    
    Optional<UserNotificationPreference> findByUserIdAndTenantIdAndCategoryAndSubType(Long userId, Long tenantId, NotificationCategory category, NotificationSubType subType);
    
    @Query("SELECT p FROM UserNotificationPreference p WHERE p.userId = :userId AND p.tenantId = :tenantId AND p.enabled = true")
    List<UserNotificationPreference> findActivePreferences(@Param("userId") Long userId, @Param("tenantId") Long tenantId);
}
```

### 3.2 通知记录 Repository
```java
@Repository
public interface NotificationRecordRepository extends JpaRepository<NotificationRecord, Long>, JpaSpecificationExecutor<NotificationRecord> {
    
    @Query("SELECT n FROM NotificationRecord n WHERE n.recipientIds LIKE %:userId% AND n.tenantId = :tenantId")
    Page<NotificationRecord> findByUserIdAndTenantId(@Param("userId") String userId, @Param("tenantId") Long tenantId, Pageable pageable);
    
    // 注意：这里仍然使用String类型，因为recipientIds是JSON格式存储多个用户ID
    // 在Service层需要进行JSON解析和Long类型转换
}
```

### 3.3 通知发送记录 Repository
```java
@Repository
public interface NotificationSendRecordRepository extends JpaRepository<NotificationSendRecord, Long>, JpaSpecificationExecutor<NotificationSendRecord> {
    
    List<NotificationSendRecord> findByNotificationIdAndStatus(Long notificationId, SendStatus status);
    
    List<NotificationSendRecord> findByRecipientIdAndTenantId(Long recipientId, Long tenantId);
    
    @Query("SELECT COUNT(n) FROM NotificationSendRecord n WHERE n.recipientId = :recipientId AND n.status = :status AND n.tenantId = :tenantId")
    long countByRecipientIdAndStatusAndTenantId(@Param("recipientId") Long recipientId, @Param("status") SendStatus status, @Param("tenantId") Long tenantId);
}
```

## 4. Service 方法更新

### 4.1 通知中心服务
```java
@Service
public class NotificationCenterServiceImpl implements NotificationCenterService {
    
    /**
     * 发送用户通知
     */
    public NotificationRecordDTO sendUserNotification(UserNotificationRequestDTO request) {
        // 获取用户偏好
        List<UserNotificationPreference> preferences = userNotificationPreferenceRepository
            .findByUserIdAndTenantIdAndEnabledTrue(request.getUserId(), TenantContextUtil.getCurrentTenantId());
        
        // 根据偏好调整发送渠道
        List<NotificationType> channels = determineChannels(request.getChannels(), preferences);
        
        // 创建通知记录
        NotificationRecord record = new NotificationRecord();
        record.setTenantId(TenantContextUtil.getCurrentTenantId());
        record.setCategory(NotificationCategory.USER);
        record.setSubType(request.getSubType());
        record.setScope(NotificationScope.USER);
        record.setTitle(request.getTitle());
        record.setContent(request.getContent());
        record.setRecipientType(RecipientType.USER);
        record.setRecipientIds(objectMapper.writeValueAsString(Arrays.asList(request.getUserId())));
        record.setChannels(objectMapper.writeValueAsString(channels));
        record.setPriority(request.getPriority());
        record.setStatus(NotificationStatus.SCHEDULED);
        record.setScheduledTime(request.getScheduledTime() != null ? request.getScheduledTime() : Instant.now());
        record.setBusinessId(request.getBusinessId());
        record.setBusinessType(request.getBusinessType());
        record.setTemplateId(request.getTemplateId());
        record.setTemplateParams(objectMapper.writeValueAsString(request.getTemplateParams()));
        record.setRetryCount(0);
        
        // 保存并发送
        record = notificationRecordRepository.save(record);
        asyncNotificationService.sendNotificationAsync(notificationRecordMapper.toDto(record));
        
        return notificationRecordMapper.toDto(record);
    }
}
```

## 5. API 调用示例

### 5.1 发送用户通知
```bash
POST /api/notification-center/user
Content-Type: application/json

{
    "userId": 12345,
    "subType": "PASSWORD_CHANGED",
    "title": "密码修改通知",
    "content": "您的账户密码已成功修改，如非本人操作请及时联系管理员。",
    "channels": ["EMAIL", "SMS"],
    "priority": "HIGH",
    "templateParams": {
        "userName": "张三",
        "changeTime": "2025-06-23 14:30:00",
        "ipAddress": "*************"
    }
}
```

### 5.2 批量发送通知
```bash
POST /api/notification-center/batch-send
Content-Type: application/json

{
    "category": "BUSINESS",
    "subType": "TRAINING_REMINDER",
    "title": "合规培训提醒",
    "content": "您有一个合规培训即将开始，请及时参加。",
    "recipientIds": [12345, 12346, 12347, 12348],
    "channels": ["EMAIL", "PUSH"],
    "priority": "NORMAL",
    "templateParams": {
        "trainingName": "数据安全合规培训",
        "startTime": "2025-06-25 09:00:00",
        "duration": "2小时"
    },
    "batchSize": 50
}
```

所有用户ID相关的字段都已修正为 `Long` 类型，确保与您的系统保持一致。
