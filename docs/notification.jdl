/**
 * 通知模块 JDL 定义
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */

// ===================================================================
// 枚举定义
// ===================================================================

/**
 * 通知分类枚举
 */
enum NotificationCategory {
    SYSTEM,        // 系统通知
    TASK,          // 任务通知  
    USER,          // 用户通知
    BUSINESS,      // 业务通知
    SECURITY,      // 安全通知
    MAINTENANCE    // 维护通知
}

/**
 * 通知子类型枚举
 */
enum NotificationSubType {
    // 系统通知子类型
    SYSTEM_ANNOUNCEMENT,     // 系统公告
    SYSTEM_MAINTENANCE,      // 系统维护
    SYSTEM_UPGRADE,          // 系统升级
    TENANT_EXPIRED,          // 租户到期
    LICENSE_EXPIRED,         // 许可证到期
    
    // 任务通知子类型
    TASK_COMPLETED,          // 任务完成
    TASK_FAILED,             // 任务失败
    TASK_TIMEOUT,            // 任务超时
    APPROVAL_PENDING,        // 待审批
    APPROVAL_APPROVED,       // 审批通过
    APPROVAL_REJECTED,       // 审批拒绝
    
    // 用户通知子类型
    USER_LOGIN,              // 用户登录
    USER_OPERATION,          // 用户操作
    PASSWORD_CHANGED,        // 密码修改
    PROFILE_UPDATED,         // 资料更新
    
    // 业务通知子类型
    COMPLIANCE_ALERT,        // 合规告警
    TRAINING_REMINDER,       // 培训提醒
    CONTRACT_EXPIRING,       // 合同到期
    VIOLATION_REPORTED,      // 违规举报
    ORDER_STATUS_CHANGED,    // 订单状态变更
    ORG_STRUCTURE_CHANGED,   // 组织架构变更
    REGULATORY_UPDATE,       // 法规更新
    LICENSE_EXPIRING,        // 许可证即将到期
    RISK_ALERT,              // 风险告警
    AI_ANALYSIS_COMPLETED,   // AI分析完成
    DASHBOARD_ALERT,         // 仪表板告警
    ESG_REMINDER,            // ESG合规提醒
    FINANCIAL_ALERT          // 财务合规告警
}

/**
 * 通知范围枚举
 */
enum NotificationScope {
    GLOBAL,        // 全局通知（所有租户）
    TENANT,        // 租户级通知
    DEPARTMENT,    // 部门级通知
    ROLE,          // 角色级通知
    USER           // 用户级通知
}

/**
 * 通知优先级枚举
 */
enum NotificationPriority {
    LOW,           // 低优先级
    NORMAL,        // 普通优先级
    HIGH,          // 高优先级
    URGENT         // 紧急
}

/**
 * 通知状态枚举
 */
enum NotificationStatus {
    DRAFT,         // 草稿
    SCHEDULED,     // 已调度
    SENDING,       // 发送中
    SENT,          // 已发送
    FAILED,        // 发送失败
    CANCELLED      // 已取消
}

/**
 * 发送状态枚举
 */
enum SendStatus {
    PENDING,       // 待发送
    SENT,          // 已发送
    DELIVERED,     // 已送达
    READ,          // 已读
    FAILED         // 发送失败
}

/**
 * 接收者类型枚举
 */
enum RecipientType {
    USER,          // 用户
    ROLE,          // 角色
    DEPARTMENT,    // 部门
    ALL            // 全部
}

// ===================================================================
// 实体定义
// ===================================================================

/**
 * 通知记录实体
 */
entity NotificationRecord {
    /** 租户ID */
    tenantId Long,
    
    /** 通知分类 */
    category NotificationCategory required,
    
    /** 通知子类型 */
    subType NotificationSubType required,
    
    /** 通知范围 */
    scope NotificationScope required,
    
    /** 通知标题 */
    title String required maxlength(200),
    
    /** 通知内容 */
    content TextBlob,
    
    /** 接收者类型 */
    recipientType RecipientType required,
    
    /** 接收者ID列表(JSON格式) */
    recipientIds TextBlob,
    
    /** 发送渠道列表(JSON格式) */
    channels String maxlength(500),
    
    /** 优先级 */
    priority NotificationPriority required,
    
    /** 状态 */
    status NotificationStatus required,
    
    /** 计划发送时间 */
    scheduledTime Instant,
    
    /** 实际发送时间 */
    sentTime Instant,
    
    /** 关联业务ID */
    businessId String maxlength(100),
    
    /** 业务类型 */
    businessType String maxlength(50),
    
    /** 模板参数(JSON格式) */
    templateParams TextBlob,
    
    /** 重试次数 */
    retryCount Integer min(0) max(10),
    
    /** 错误信息 */
    errorMessage String maxlength(1000)
}

/**
 * 通知模板实体
 */
entity NotificationTemplate {
    /** 租户ID */
    tenantId Long,
    
    /** 模板编码 */
    code String required unique maxlength(100),
    
    /** 模板名称 */
    name String required maxlength(200),
    
    /** 通知分类 */
    category NotificationCategory required,
    
    /** 通知子类型 */
    subType NotificationSubType required,
    
    /** 标题模板 */
    titleTemplate String maxlength(500),
    
    /** 内容模板 */
    contentTemplate TextBlob,
    
    /** 短信模板 */
    smsTemplate String maxlength(500),
    
    /** 邮件模板 */
    emailTemplate TextBlob,
    
    /** 推送模板 */
    pushTemplate String maxlength(500),
    
    /** 支持的渠道(JSON格式) */
    supportedChannels String maxlength(200),
    
    /** 默认渠道(JSON格式) */
    defaultChannels String maxlength(200),
    
    /** 是否启用 */
    enabled Boolean required,
    
    /** 语言 */
    language String maxlength(10)
}

/**
 * 用户通知偏好实体
 */
entity UserNotificationPreference {
    /** 租户ID */
    tenantId Long,
    
    /** 用户ID */
    userId String required maxlength(50),
    
    /** 通知分类 */
    category NotificationCategory required,
    
    /** 通知子类型 */
    subType NotificationSubType,
    
    /** 启用的渠道(JSON格式) */
    enabledChannels String maxlength(200),
    
    /** 免打扰开始时间 */
    quietHoursStart String maxlength(5),
    
    /** 免打扰结束时间 */
    quietHoursEnd String maxlength(5),
    
    /** 是否启用 */
    enabled Boolean required
}

/**
 * 通知发送记录实体
 */
entity NotificationSendRecord {
    /** 接收者ID */
    recipientId String required maxlength(50),
    
    /** 接收者类型 */
    recipientType RecipientType required,
    
    /** 发送渠道 */
    channel NotificationType required,
    
    /** 发送状态 */
    status SendStatus required,
    
    /** 发送时间 */
    sentTime Instant,
    
    /** 阅读时间 */
    readTime Instant,
    
    /** 错误信息 */
    errorMessage String maxlength(1000),
    
    /** 第三方服务返回的ID */
    externalId String maxlength(100)
}

// ===================================================================
// 关系定义
// ===================================================================

relationship ManyToOne {
    NotificationSendRecord{notification} to NotificationRecord{sendRecords},
    NotificationRecord{template} to NotificationTemplate
}

// ===================================================================
// 配置选项
// ===================================================================

// 分页配置
paginate NotificationRecord, NotificationSendRecord with pagination
paginate NotificationTemplate, UserNotificationPreference with infinite-scroll

// 服务层配置
service all with serviceImpl

// DTO配置
dto all with mapstruct

// 过滤器配置
filter NotificationRecord, NotificationSendRecord, UserNotificationPreference
