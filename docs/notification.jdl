/**
 * 通知模块 JDL 定义
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-23
 */

// ===================================================================
// 枚举定义
// ===================================================================

/**
 * 通知分类枚举
 */
enum NotificationCategory {
    /** 系统通知 */ SYSTEM,
    /** 任务通知 */ TASK,
    /** 用户通知 */ USER,
    /** 业务通知 */ BUSINESS,
    /** 安全通知 */ SECURITY,
    /** 维护通知 */ MAINTENANCE
}

/**
 * 通知子类型枚举
 */
enum NotificationSubType {
    /** 系统公告 */ SYSTEM_ANNOUNCEMENT,
    /** 系统维护 */ SYSTEM_MAINTENANCE,
    /** 系统升级 */ SYSTEM_UPGRADE,
    /** 租户到期 */ TENANT_EXPIRED,
    /** 许可证到期 */ LICENSE_EXPIRED,
    /** 任务完成 */ TASK_COMPLETED,
    /** 任务失败 */ TASK_FAILED,
    /** 任务超时 */ TASK_TIMEOUT,
    /** 待审批 */ APPROVAL_PENDING,
    /** 审批通过 */ APPROVAL_APPROVED,
    /** 审批拒绝 */ APPROVAL_REJECTED,
    /** 审批超时 */ APPROVAL_TIMEOUT,
    /** 审批撤回 */ APPROVAL_WITHDRAWN,
    /** 用户登录 */ USER_LOGIN,
    /** 用户操作 */ USER_OPERATION,
    /** 密码修改 */ PASSWORD_CHANGED,
    /** 密码即将过期 */ PASSWORD_EXPIRING,
    /** 账户锁定 */ ACCOUNT_LOCKED,
    /** 资料更新 */ PROFILE_UPDATED,
    /** 合规告警 */ COMPLIANCE_ALERT,
    /** 培训提醒 */ TRAINING_REMINDER,
    /** 培训完成 */ TRAINING_COMPLETED,
    /** 考试安排 */ EXAM_SCHEDULED,
    /** 考试成绩发布 */ EXAM_RESULT_PUBLISHED,
    /** 合同到期 */ CONTRACT_EXPIRING,
    /** 合同审批待处理 */ CONTRACT_APPROVAL_PENDING,
    /** 合同续签 */ CONTRACT_RENEWED,
    /** 合同修订 */ CONTRACT_AMENDED,
    /** 违规举报 */ VIOLATION_REPORTED,
    /** 违规处理中 */ VIOLATION_PROCESSING,
    /** 违规已解决 */ VIOLATION_RESOLVED,
    /** 订单状态变更 */ ORDER_STATUS_CHANGED,
    /** 订单支付失败 */ ORDER_PAYMENT_FAILED,
    /** 服务续费提醒 */ SERVICE_RENEWAL_REMINDER,
    /** 组织架构变更 */ ORG_STRUCTURE_CHANGED,
    /** 人员变动 */ PERSONNEL_CHANGE,
    /** 法规更新 */ REGULATORY_UPDATE,
    /** 合规截止日期 */ COMPLIANCE_DEADLINE,
    /** 法规影响分析 */ REGULATORY_IMPACT_ANALYSIS,
    /** 许可证即将到期 */ LICENSE_EXPIRING,
    /** 许可证使用量告警 */ LICENSE_USAGE_ALERT,
    /** 许可证续期 */ LICENSE_RENEWED,
    /** 风险告警 */ RISK_ALERT,
    /** 风险等级变更 */ RISK_LEVEL_CHANGED,
    /** 风险已缓解 */ RISK_MITIGATED,
    /** AI分析完成 */ AI_ANALYSIS_COMPLETED,
    /** AI模型更新 */ AI_MODEL_UPDATED,
    /** AI异常检测 */ AI_ANOMALY_DETECTED,
    /** 仪表板告警 */ DASHBOARD_ALERT,
    /** 数据异常检测 */ DATA_ANOMALY_DETECTED,
    /** 报表生成 */ REPORT_GENERATED,
    /** 阈值超限 */ THRESHOLD_EXCEEDED,
    /** ESG合规提醒 */ ESG_REMINDER,
    /** ESG评估到期 */ ESG_ASSESSMENT_DUE,
    /** ESG指标更新 */ ESG_METRICS_UPDATED,
    /** ESG报告发布 */ ESG_REPORT_PUBLISHED,
    /** 财务合规告警 */ FINANCIAL_ALERT,
    /** 财务审计到期 */ FINANCIAL_AUDIT_DUE,
    /** 财务合规检查 */ FINANCIAL_COMPLIANCE_CHECK,
    /** 财务数据异常 */ FINANCIAL_ANOMALY,
    /** 用户账户操作 */ USER_ACCOUNT_OPERATION,
    /** 系统性能告警 */ SYSTEM_PERFORMANCE_ALERT,
    /** 合规清单更新 */ COMPLIANCE_LIST_UPDATED,
    /** 合规检查提醒 */ COMPLIANCE_CHECK_REMINDER,
    /** 合规清单即将到期 */ COMPLIANCE_LIST_EXPIRING
}

/**
 * 通知范围枚举
 */
enum NotificationScope {
    /** 全局通知（所有租户） */ GLOBAL,
    /** 租户级通知 */ TENANT,
    /** 部门级通知 */ DEPARTMENT,
    /** 角色级通知 */ ROLE,
    /** 用户级通知 */ USER
}

/**
 * 通知优先级枚举
 */
enum NotificationPriority {
    /** 低优先级 */ LOW,
    /** 普通优先级 */ NORMAL,
    /** 高优先级 */ HIGH,
    /** 紧急 */ URGENT
}

/**
 * 通知状态枚举
 */
enum NotificationStatus {
    /** 草稿 */ DRAFT,
    /** 已调度 */ SCHEDULED,
    /** 发送中 */ SENDING,
    /** 已发送 */ SENT,
    /** 发送失败 */ FAILED,
    /** 已取消 */ CANCELLED
}

/**
 * 发送状态枚举
 */
enum SendStatus {
    /** 待发送 */ PENDING,
    /** 已发送 */ SENT,
    /** 已送达 */ DELIVERED,
    /** 已读 */ READ,
    /** 发送失败 */ FAILED
}

/**
 * 接收者类型枚举
 */
enum RecipientType {
    /** 用户 */ USER,
    /** 角色 */ ROLE,
    /** 部门 */ DEPARTMENT,
    /** 全部 */ ALL
}

/**
 * 通知类型枚举
 */
enum NotificationType {
  /** * 短信通知 */ SMS,
  /** * 邮件通知 */ EMAIL,
  /** * APP推送通知 */ PUSH,
  /** * 微信公众号推送 */ WECHAT
}

// ===================================================================
// 实体定义
// ===================================================================

/** 通知记录实体 */
/** 存储通知发送记录和状态信息 */
entity NotificationRecord {
    /** 主键ID */
    id Long required,
    /** 租户ID（0 = 平台级） */
    tenantId Long required,
    /** 通知分类 */
    category NotificationCategory required,
    /** 通知子类型 */
    subType NotificationSubType required,
    /** 通知范围 */
    scope NotificationScope required,
    /** 通知标题 */
    title String required maxlength(200),
    /** 通知内容 */
    content String maxlength(2000),
    /** 接收者类型 */
    recipientType RecipientType required,
    /** 接收者ID列表(JSON格式) */
    recipientIds String maxlength(2000),
    /** 发送渠道列表(JSON格式) */
    channels String maxlength(500),
    /** 优先级 */
    priority NotificationPriority required,
    /** 状态 */
    status NotificationStatus required,
    /** 计划发送时间 */
    scheduledTime Instant,
    /** 实际发送时间 */
    sentTime Instant,
    /** 关联业务ID */
    businessId String maxlength(100),
    /** 业务类型 */
    businessType String maxlength(50),
    /** 模板参数(JSON格式) */
    templateParams String maxlength(2000),
    /** 重试次数 */
    retryCount Integer min(0) max(10),
    /** 错误信息 */
    errorMessage String maxlength(1000),
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/** 通知模板实体 */
/** 存储通知模板配置信息 */
entity NotificationTemplate {
    /** 主键ID */
    id Long required,
    /** 租户ID（0 = 平台级） */
    tenantId Long required,
    /** 模板编码 */
    code String required unique maxlength(100),
    /** 模板名称 */
    name String required maxlength(200),
    /** 通知分类 */
    category NotificationCategory required,
    /** 通知子类型 */
    subType NotificationSubType required,
    /** 标题模板 */
    titleTemplate String maxlength(500),
    /** 内容模板 */
    contentTemplate String maxlength(2000),
    /** 短信模板 */
    smsTemplate String maxlength(500),
    /** 邮件模板 */
    emailTemplate String maxlength(2000),
    /** 推送模板 */
    pushTemplate String maxlength(500),
    /** 支持的渠道(JSON格式) */
    supportedChannels String maxlength(200),
    /** 默认渠道(JSON格式) */
    defaultChannels String maxlength(200),
    /** 是否启用 */
    enabled Boolean required,
    /** 语言 */
    language String maxlength(10),
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/** 用户通知偏好实体 */
/** 存储用户的通知偏好设置 */
entity UserNotificationPreference {
    /** 主键ID */
    id Long required,
    /** 租户ID（0 = 平台级） */
    tenantId Long required,
    /** 用户ID */
    userId String required maxlength(50),
    /** 通知分类 */
    category NotificationCategory required,
    /** 通知子类型 */
    subType NotificationSubType,
    /** 启用的渠道(JSON格式) */
    enabledChannels String maxlength(200),
    /** 免打扰开始时间 */
    quietHoursStart String maxlength(5),
    /** 免打扰结束时间 */
    quietHoursEnd String maxlength(5),
    /** 是否启用 */
    enabled Boolean required,
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

/** 通知发送记录实体 */
/** 存储每个接收者的通知发送详细记录 */
entity NotificationSendRecord {
    /** 主键ID */
    id Long required,
    /** 租户ID（0 = 平台级） */
    tenantId Long required,
    /** 接收者ID */
    recipientId String required maxlength(50),
    /** 接收者类型 */
    recipientType RecipientType required,
    /** 发送渠道 */
    channel NotificationType required,
    /** 发送状态 */
    status SendStatus required,
    /** 发送时间 */
    sentTime Instant,
    /** 阅读时间 */
    readTime Instant,
    /** 错误信息 */
    errorMessage String maxlength(1000),
    /** 第三方服务返回的ID */
    externalId String maxlength(100),
    /** 乐观锁版本 */
    version Integer required,
    /** 创建者 */
    createdBy String maxlength(50),
    /** 创建时间 */
    createdAt Instant required,
    /** 更新者 */
    updatedBy String maxlength(50),
    /** 更新时间 */
    updatedAt Instant required,
    /** 软删除标志 */
    isDeleted Boolean required
}

// ===================================================================
// 关系定义
// ===================================================================

relationship ManyToOne {
    NotificationSendRecord{notification} to NotificationRecord{sendRecords},
    NotificationRecord{template} to NotificationTemplate
}

// ===================================================================
// 配置选项
// ===================================================================

/* 生成选项 */
dto * with mapstruct
service all with serviceImpl
paginate * with pagination
