# 通知模块服务架构设计

## 1. JHipster 标准分层架构

### 1.1 Repository 层
```java
// 通知记录仓库
@Repository
public interface NotificationRecordRepository extends JpaRepository<NotificationRecord, Long>, JpaSpecificationExecutor<NotificationRecord> {
    
    @Query("SELECT n FROM NotificationRecord n WHERE n.tenantId = :tenantId AND n.status = :status")
    Page<NotificationRecord> findByTenantIdAndStatus(@Param("tenantId") Long tenantId, 
                                                    @Param("status") NotificationStatus status, 
                                                    Pageable pageable);
    
    @Query("SELECT n FROM NotificationRecord n WHERE n.recipientIds LIKE %:userId% AND n.tenantId = :tenantId")
    Page<NotificationRecord> findByUserIdAndTenantId(@Param("userId") String userId, 
                                                     @Param("tenantId") Long tenantId, 
                                                     Pageable pageable);
    
    @Modifying
    @Query("UPDATE NotificationRecord n SET n.status = :status WHERE n.id = :id")
    int updateStatus(@Param("id") Long id, @Param("status") NotificationStatus status);
}

// 通知模板仓库
@Repository
public interface NotificationTemplateRepository extends JpaRepository<NotificationTemplate, Long>, JpaSpecificationExecutor<NotificationTemplate> {
    
    Optional<NotificationTemplate> findByCodeAndTenantId(String code, Long tenantId);
    
    Optional<NotificationTemplate> findByCodeAndTenantIdIsNull(String code);
    
    List<NotificationTemplate> findByCategoryAndSubTypeAndEnabledTrue(NotificationCategory category, NotificationSubType subType);
}

// 用户通知偏好仓库
@Repository
public interface UserNotificationPreferenceRepository extends JpaRepository<UserNotificationPreference, Long>, JpaSpecificationExecutor<UserNotificationPreference> {
    
    List<UserNotificationPreference> findByUserIdAndTenantIdAndEnabledTrue(String userId, Long tenantId);
    
    Optional<UserNotificationPreference> findByUserIdAndTenantIdAndCategoryAndSubType(String userId, Long tenantId, NotificationCategory category, NotificationSubType subType);
}

// 通知发送记录仓库
@Repository
public interface NotificationSendRecordRepository extends JpaRepository<NotificationSendRecord, Long>, JpaSpecificationExecutor<NotificationSendRecord> {
    
    List<NotificationSendRecord> findByNotificationIdAndStatus(Long notificationId, SendStatus status);
    
    @Query("SELECT COUNT(n) FROM NotificationSendRecord n WHERE n.notification.id = :notificationId AND n.status = :status")
    long countByNotificationIdAndStatus(@Param("notificationId") Long notificationId, @Param("status") SendStatus status);
}
```

### 1.2 DTO 层
```java
// 通知记录 DTO
public class NotificationRecordDTO {
    private Long id;
    private Long tenantId;
    private NotificationCategory category;
    private NotificationSubType subType;
    private NotificationScope scope;
    private String title;
    private String content;
    private RecipientType recipientType;
    private String recipientIds;
    private String channels;
    private NotificationPriority priority;
    private NotificationStatus status;
    private Instant scheduledTime;
    private Instant sentTime;
    private String businessId;
    private String businessType;
    private Long templateId;
    private String templateParams;
    private Integer retryCount;
    private String errorMessage;
    private Instant createdDate;
    private String createdBy;
    private Instant lastModifiedDate;
    private String lastModifiedBy;
    
    // 关联数据
    private NotificationTemplateDTO template;
    private List<NotificationSendRecordDTO> sendRecords;
}

// 通知请求 DTO
public class NotificationRequestDTO {
    @NotNull
    private NotificationCategory category;
    
    @NotNull
    private NotificationSubType subType;
    
    @NotNull
    private NotificationScope scope;
    
    @NotBlank
    @Size(max = 200)
    private String title;
    
    private String content;
    
    @NotNull
    private RecipientType recipientType;
    
    private List<String> recipientIds;
    
    private List<NotificationType> channels;
    
    @NotNull
    private NotificationPriority priority;
    
    private Instant scheduledTime;
    
    private String businessId;
    
    private String businessType;
    
    private Long templateId;
    
    private Map<String, Object> templateParams;
}

// 批量通知请求 DTO
public class BatchNotificationRequestDTO {
    @NotNull
    private NotificationCategory category;
    
    @NotNull
    private NotificationSubType subType;
    
    @NotBlank
    private String title;
    
    private String content;
    
    @NotEmpty
    private List<String> recipientIds;
    
    private List<NotificationType> channels;
    
    @NotNull
    private NotificationPriority priority;
    
    private Instant scheduledTime;
    
    private String businessId;
    
    private String businessType;
    
    private Long templateId;
    
    private Map<String, Object> templateParams;
    
    @Min(1)
    @Max(1000)
    private Integer batchSize = 100;
}
```

### 1.3 Mapper 层
```java
@Mapper(componentModel = "spring", uses = {})
public interface NotificationRecordMapper extends EntityMapper<NotificationRecordDTO, NotificationRecord> {
    
    @Mapping(target = "sendRecords", ignore = true)
    @Mapping(target = "template", ignore = true)
    NotificationRecordDTO toDto(NotificationRecord notificationRecord);
    
    @Mapping(target = "sendRecords", ignore = true)
    @Mapping(target = "template", ignore = true)
    NotificationRecord toEntity(NotificationRecordDTO notificationRecordDTO);
    
    default NotificationRecord fromId(Long id) {
        if (id == null) {
            return null;
        }
        NotificationRecord notificationRecord = new NotificationRecord();
        notificationRecord.setId(id);
        return notificationRecord;
    }
}

@Mapper(componentModel = "spring", uses = {})
public interface NotificationTemplateMapper extends EntityMapper<NotificationTemplateDTO, NotificationTemplate> {
    
    NotificationTemplateDTO toDto(NotificationTemplate notificationTemplate);
    
    NotificationTemplate toEntity(NotificationTemplateDTO notificationTemplateDTO);
    
    default NotificationTemplate fromId(Long id) {
        if (id == null) {
            return null;
        }
        NotificationTemplate notificationTemplate = new NotificationTemplate();
        notificationTemplate.setId(id);
        return notificationTemplate;
    }
}
```

### 1.4 Service 层
```java
// 通知记录服务接口
public interface NotificationRecordService {
    
    /**
     * 保存通知记录
     */
    NotificationRecordDTO save(NotificationRecordDTO notificationRecordDTO);
    
    /**
     * 更新通知记录
     */
    NotificationRecordDTO update(NotificationRecordDTO notificationRecordDTO);
    
    /**
     * 部分更新通知记录
     */
    Optional<NotificationRecordDTO> partialUpdate(NotificationRecordDTO notificationRecordDTO);
    
    /**
     * 获取所有通知记录
     */
    Page<NotificationRecordDTO> findAll(Pageable pageable);
    
    /**
     * 根据条件查询通知记录
     */
    Page<NotificationRecordDTO> findAllWithEagerRelationships(Pageable pageable);
    
    /**
     * 根据ID获取通知记录
     */
    Optional<NotificationRecordDTO> findOne(Long id);
    
    /**
     * 删除通知记录
     */
    void delete(Long id);
    
    /**
     * 根据用户ID和租户ID查询通知记录
     */
    Page<NotificationRecordDTO> findByUserIdAndTenantId(String userId, Long tenantId, Pageable pageable);
    
    /**
     * 根据状态查询通知记录
     */
    Page<NotificationRecordDTO> findByTenantIdAndStatus(Long tenantId, NotificationStatus status, Pageable pageable);
}

// 通知中心服务接口
public interface NotificationCenterService {
    
    /**
     * 发送单个通知
     */
    NotificationRecordDTO sendNotification(NotificationRequestDTO request);
    
    /**
     * 批量发送通知
     */
    String sendBatchNotification(BatchNotificationRequestDTO request);
    
    /**
     * 发送系统通知
     */
    NotificationRecordDTO sendSystemNotification(SystemNotificationRequestDTO request);
    
    /**
     * 发送任务通知
     */
    NotificationRecordDTO sendTaskNotification(TaskNotificationRequestDTO request);
    
    /**
     * 发送用户通知
     */
    NotificationRecordDTO sendUserNotification(UserNotificationRequestDTO request);
    
    /**
     * 重新发送失败的通知
     */
    void retryFailedNotifications();
    
    /**
     * 取消计划中的通知
     */
    void cancelScheduledNotification(Long notificationId);
}
```

## 2. 异步处理和消息队列

### 2.1 异步配置
```java
@Configuration
@EnableAsync
public class NotificationAsyncConfig {
    
    @Bean(name = "notificationTaskExecutor")
    public TaskExecutor notificationTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("notification-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}

@Service
public class AsyncNotificationService {
    
    @Async("notificationTaskExecutor")
    public CompletableFuture<Void> sendNotificationAsync(NotificationRecordDTO notification) {
        // 异步发送通知逻辑
        return CompletableFuture.completedFuture(null);
    }
    
    @Async("notificationTaskExecutor")
    public CompletableFuture<Void> sendBatchNotificationAsync(List<NotificationRecordDTO> notifications) {
        // 异步批量发送通知逻辑
        return CompletableFuture.completedFuture(null);
    }
}
```

### 2.2 事件驱动架构
```java
// 通知事件
public class NotificationEvent extends ApplicationEvent {
    private final NotificationRequestDTO notificationRequest;
    
    public NotificationEvent(Object source, NotificationRequestDTO notificationRequest) {
        super(source);
        this.notificationRequest = notificationRequest;
    }
}

// 事件监听器
@Component
public class NotificationEventListener {
    
    @EventListener
    @Async
    public void handleNotificationEvent(NotificationEvent event) {
        // 处理通知事件
        notificationCenterService.sendNotification(event.getNotificationRequest());
    }
    
    @EventListener
    @Async
    public void handleApprovalEvent(ApprovalCompletedEvent event) {
        // 处理审批完成事件，发送通知
    }
    
    @EventListener
    @Async
    public void handleTaskEvent(TaskCompletedEvent event) {
        // 处理任务完成事件，发送通知
    }
}
```

## 3. 缓存策略

### 3.1 缓存配置
```java
@Configuration
@EnableCaching
public class NotificationCacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
            .RedisCacheManagerBuilder
            .fromConnectionFactory(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration());
        
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}

@Service
public class NotificationTemplateService {
    
    @Cacheable(value = "notificationTemplates", key = "#code + '_' + #tenantId")
    public Optional<NotificationTemplateDTO> findByCodeAndTenantId(String code, Long tenantId) {
        // 查询模板逻辑
    }
    
    @CacheEvict(value = "notificationTemplates", allEntries = true)
    public NotificationTemplateDTO save(NotificationTemplateDTO template) {
        // 保存模板逻辑
    }
}
```

这个重新设计的方案如何？它完全符合JHipster规范，覆盖了所有23个微服务的通知需求，并且提供了完整的分层架构、异步处理、缓存策略等企业级功能。您觉得还需要调整哪些地方？
